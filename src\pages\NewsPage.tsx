import { ArrowRight, Calendar, Tag, User } from 'lucide-react';
import React, { useState } from 'react';

const NewsPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('All');

  const categories = ['All', 'Company News', 'Industry Insights', 'Innovation', 'Sustainability'];

  const articles = [
    {
      id: 1,
      title: 'WINASTRA Achieves Carbon Neutral Manufacturing',
      excerpt: 'Milestone achievement in our sustainability journey with 100% renewable energy adoption.',
      category: 'Sustainability',
      author: '<PERSON>',
      date: '2024-01-15',
      readTime: '5 min read',
      image: 'https://images.pexels.com/photos/1108124/pexels-photo-1108124.jpeg?auto=compress&cs=tinysrgb&w=600',
      featured: true
    },
    {
      id: 2,
      title: 'Revolutionary Nano-Surface Technology Launch',
      excerpt: 'Introducing breakthrough surface treatment technology that increases durability by 300%.',
      category: 'Innovation',
      author: 'Dr. <PERSON>',
      date: '2024-01-10',
      readTime: '7 min read',
      image: 'https://images.pexels.com/photos/1108125/pexels-photo-1108125.jpeg?auto=compress&cs=tinysrgb&w=600',
      featured: true
    },
    {
      id: 3,
      title: 'Aerospace Partnership Expansion',
      excerpt: 'New strategic partnerships with leading aerospace manufacturers for next-generation components.',
      category: 'Company News',
      author: 'Robert Martinez',
      date: '2024-01-05',
      readTime: '4 min read',
      image: 'https://images.pexels.com/photos/1108126/pexels-photo-1108126.jpeg?auto=compress&cs=tinysrgb&w=600',
      featured: false
    },
    {
      id: 4,
      title: 'Industry 4.0: The Future of Aluminum Manufacturing',
      excerpt: 'How AI and automation are transforming aluminum production processes.',
      category: 'Industry Insights',
      author: 'Lisa Thompson',
      date: '2023-12-28',
      readTime: '6 min read',
      image: 'https://images.pexels.com/photos/1108127/pexels-photo-1108127.jpeg?auto=compress&cs=tinysrgb&w=600',
      featured: false
    },
    {
      id: 5,
      title: 'Q4 2023 Performance Highlights',
      excerpt: 'Record-breaking quarter with 25% growth in production capacity and new market expansion.',
      category: 'Company News',
      author: 'David Wilson',
      date: '2023-12-20',
      readTime: '3 min read',
      image: 'https://images.pexels.com/photos/1108128/pexels-photo-1108128.jpeg?auto=compress&cs=tinysrgb&w=600',
      featured: false
    },
    {
      id: 6,
      title: 'Sustainable Aluminum: Circular Economy Principles',
      excerpt: 'How aluminum recycling contributes to a more sustainable manufacturing ecosystem.',
      category: 'Sustainability',
      author: 'Emma Green',
      date: '2023-12-15',
      readTime: '5 min read',
      image: 'https://images.pexels.com/photos/1108129/pexels-photo-1108129.jpeg?auto=compress&cs=tinysrgb&w=600',
      featured: false
    }
  ];

  const filteredArticles = activeCategory === 'All'
    ? articles
    : articles.filter(article => article.category === activeCategory);

  const featuredArticles = articles.filter(article => article.featured);
  const regularArticles = filteredArticles.filter(article => !article.featured);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link sr-only focus:not-sr-only">
        Skip to main content
      </a>

      {/* Hero Section */}
      <section id="main-content" className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.pexels.com/photos/1108130/pexels-photo-1108130.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="News and insights"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">NEWS &</span>
            <br />
            <span className="text-white font-light">INSIGHTS</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Stay informed with the latest developments in aluminum manufacturing,
            industry trends, and WINASTRA innovations.
          </p>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-12 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4 animate-on-scroll">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${activeCategory === category
                  ? 'metallic-button'
                  : 'glass-effect text-gray-300 hover:text-white'
                  }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      {activeCategory === 'All' && (
        <section className="py-20 bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16 animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
                Featured Stories
              </h2>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {featuredArticles.map((article, index) => (
                <div
                  key={article.id}
                  className="group hover-lift animate-on-scroll"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="glass-effect rounded-2xl overflow-hidden h-full">
                    <div className="relative overflow-hidden">
                      <img
                        src={article.image}
                        alt={article.title}
                        className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-gradient-to-r from-gray-400 to-yellow-500 text-black text-xs rounded-full font-medium">
                          Featured
                        </span>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="flex items-center space-x-4 mb-4 text-sm text-gray-400">
                        <span className="flex items-center space-x-1">
                          <Tag size={14} />
                          <span>{article.category}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Calendar size={14} />
                          <span>{new Date(article.date).toLocaleDateString()}</span>
                        </span>
                        <span>{article.readTime}</span>
                      </div>

                      <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-gray-300 transition-colors duration-300">
                        {article.title}
                      </h3>

                      <p className="text-gray-400 mb-4 leading-relaxed">
                        {article.excerpt}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-sm text-gray-400">
                          <User size={14} />
                          <span>{article.author}</span>
                        </div>

                        <button className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                          <span className="text-sm">Read More</span>
                          <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-300" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Regular Articles */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularArticles.map((article, index) => (
              <div
                key={article.id}
                className="group hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="glass-effect rounded-2xl overflow-hidden h-full">
                  <div className="relative overflow-hidden">
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-white/10 backdrop-blur-md text-white text-xs rounded-full">
                        {article.category}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center space-x-4 mb-3 text-xs text-gray-400">
                      <span className="flex items-center space-x-1">
                        <Calendar size={12} />
                        <span>{new Date(article.date).toLocaleDateString()}</span>
                      </span>
                      <span>{article.readTime}</span>
                    </div>

                    <h3 className="text-lg font-semibold text-white mb-3 group-hover:text-gray-300 transition-colors duration-300">
                      {article.title}
                    </h3>

                    <p className="text-gray-400 mb-4 leading-relaxed text-sm">
                      {article.excerpt}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-xs text-gray-400">
                        <User size={12} />
                        <span>{article.author}</span>
                      </div>

                      <button className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                        <span className="text-xs">Read</span>
                        <ArrowRight size={14} className="group-hover:translate-x-1 transition-transform duration-300" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <h3 className="text-3xl font-light text-white mb-6">
              Stay Updated
            </h3>
            <p className="text-gray-400 mb-8 leading-relaxed">
              Subscribe to our newsletter for the latest news, insights, and innovations
              in aluminum manufacturing.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none text-white placeholder-gray-500"
              />
              <button className="metallic-button px-6 py-3 rounded-lg font-medium">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NewsPage;