import React from 'react';
import { Atom, Cpu, Microscope, Zap } from 'lucide-react';

const InnovationSection: React.FC = () => {
  const innovations = [
    {
      icon: Atom,
      title: 'Advanced Alloy Development',
      description: 'Proprietary aluminum alloys engineered for superior performance',
      metric: '50+',
      unit: 'Custom Alloys'
    },
    {
      icon: Cpu,
      title: 'AI-Driven Manufacturing',
      description: 'Machine learning optimization for precision and efficiency',
      metric: '99.8%',
      unit: 'Accuracy Rate'
    },
    {
      icon: Microscope,
      title: 'Nano-Surface Technology',
      description: 'Revolutionary surface treatments at the molecular level',
      metric: '10x',
      unit: 'Durability Increase'
    },
    {
      icon: Zap,
      title: 'Energy-Efficient Processes',
      description: 'Sustainable manufacturing with reduced environmental impact',
      metric: '40%',
      unit: 'Energy Reduction'
    }
  ];

  return (
    <section id="innovation" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            Innovation
            <span className="block metallic-gradient font-light">Laboratory</span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Pushing the boundaries of aluminum technology through continuous research, 
            development, and breakthrough innovations.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {innovations.map((innovation, index) => (
            <div
              key={index}
              className="text-center group animate-on-scroll"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="glass-effect rounded-2xl p-8 hover-lift">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                  <innovation.icon className="w-8 h-8 text-black" />
                </div>
                
                <div className="text-3xl font-light metallic-gradient mono-font mb-2">
                  {innovation.metric}
                </div>
                <div className="text-sm text-gray-500 uppercase tracking-wider mb-4">
                  {innovation.unit}
                </div>
                
                <h3 className="text-lg font-semibold text-white mb-4">
                  {innovation.title}
                </h3>
                
                <p className="text-gray-400 leading-relaxed text-sm">
                  {innovation.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Research & Development */}
        <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-light text-white mb-6">
                Research & Development
              </h3>
              <p className="text-gray-400 mb-8 leading-relaxed">
                Our state-of-the-art R&D facility is where tomorrow's aluminum solutions 
                are born today. We invest heavily in cutting-edge research to maintain 
                our position at the forefront of aluminum technology.
              </p>
              
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                  <span className="text-gray-300">Advanced metallurgy research</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                  <span className="text-gray-300">Sustainable manufacturing processes</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                  <span className="text-gray-300">Next-generation surface treatments</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                  <span className="text-gray-300">AI-driven quality optimization</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <img
                src="https://images.pexels.com/photos/1108114/pexels-photo-1108114.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Research laboratory for aluminum technology advancement"
                className="w-full h-96 object-cover rounded-2xl"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent rounded-2xl"></div>
              <div className="absolute top-6 left-6">
                <div className="glass-effect rounded-lg p-4">
                  <div className="text-2xl font-light metallic-gradient mono-font">24/7</div>
                  <div className="text-xs text-gray-400 uppercase tracking-wider">Research Active</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InnovationSection;