import { ChevronDown, Play, Sparkles } from 'lucide-react';
import React, { useEffect, useState } from 'react';

const PremiumHeroSection: React.FC = () => {
  const [scrollY, setScrollY] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setScrollY(window.scrollY);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleExploreClick = () => {
    const manufacturingSection = document.getElementById('manufacturing');
    if (manufacturingSection) {
      manufacturingSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Premium Background with Parallax */}
      <div className="absolute inset-0 z-0">
        <div className="relative w-full h-full">
          <img
            src="https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="Precision aluminum manufacturing"
            className="w-full h-full object-cover"
            style={{ 
              transform: `translateY(${scrollY * 0.3}px) scale(${1 + scrollY * 0.0002})`,
              filter: 'brightness(0.7) contrast(1.1)'
            }}
          />
          
          {/* Premium Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/40 to-black/90"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-black/60"></div>
          
          {/* Luxury Pattern Overlay */}
          <div 
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.3) 0%, transparent 50%),
                               radial-gradient(circle at 75% 75%, rgba(192, 192, 192, 0.2) 0%, transparent 50%)`,
              backgroundSize: '400px 400px',
              animation: 'luxuryFloat 8s ease-in-out infinite'
            }}
          ></div>
        </div>
      </div>

      {/* Premium Content */}
      <div className="relative z-10 text-center max-w-7xl mx-auto px-4">
        <div className={`transition-all duration-1200 ${isLoaded ? 'luxury-animate-fade-up' : 'opacity-0'}`}>
          {/* Luxury Brand Mark */}
          <div className="flex items-center justify-center mb-8">
            <Sparkles className="w-6 h-6 text-yellow-400 mr-3 luxury-animate-float" />
            <span className="luxury-caption text-gray-300">Premium Aluminum Manufacturing</span>
            <Sparkles className="w-6 h-6 text-yellow-400 ml-3 luxury-animate-float" style={{ animationDelay: '1s' }} />
          </div>

          {/* Premium Typography Hierarchy */}
          <h1 className="luxury-heading-display text-6xl md:text-8xl lg:text-9xl mb-8 tracking-tight leading-none">
            <span 
              className="luxury-gradient-platinum block"
              style={{ animationDelay: '0.3s' }}
            >
              PRECISION
            </span>
            <span 
              className="luxury-gradient-gold block font-light mt-2"
              style={{ animationDelay: '0.6s' }}
            >
              CRAFTED
            </span>
          </h1>

          {/* Enhanced Subtitle */}
          <div className="mb-6" style={{ animationDelay: '0.9s' }}>
            <p className="luxury-body-large text-gray-200 mb-2 max-w-4xl mx-auto">
              Where Engineering Excellence Meets Artisanal Mastery
            </p>
            <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto"></div>
          </div>

          <p 
            className="luxury-body-medium text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed"
            style={{ animationDelay: '1.2s' }}
          >
            Transforming premium aluminum into extraordinary solutions through cutting-edge technology, 
            uncompromising quality, and decades of specialized expertise.
          </p>

          {/* Premium CTA Section */}
          <div 
            className="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8 mb-16"
            style={{ animationDelay: '1.5s' }}
          >
            <button
              onClick={handleExploreClick}
              className="luxury-button-primary group relative overflow-hidden"
              aria-label="Explore our manufacturing capabilities"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>Explore Excellence</span>
                <ChevronDown size={18} className="group-hover:translate-y-1 transition-transform duration-300" />
              </span>
            </button>

            <button
              className="luxury-button-secondary group flex items-center space-x-3"
              aria-label="Watch our manufacturing process"
            >
              <div className="w-12 h-12 rounded-full luxury-glass-accent flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Play size={16} className="ml-1 text-yellow-400" />
              </div>
              <span className="luxury-body-medium">Watch Process</span>
            </button>
          </div>

          {/* Premium Technical Specifications */}
          <div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            style={{ animationDelay: '1.8s' }}
          >
            {[
              { value: '99.9%', label: 'Purity', description: 'Material Excellence' },
              { value: '±0.01mm', label: 'Tolerance', description: 'Precision Engineering' },
              { value: 'ISO 9001', label: 'Certified', description: 'Quality Assurance' }
            ].map((spec, index) => (
              <div 
                key={index}
                className="luxury-glass-primary rounded-2xl p-6 luxury-hover-glow group"
                style={{ animationDelay: `${2.1 + index * 0.2}s` }}
              >
                <div className="luxury-gradient-gold text-3xl font-light mono-font mb-2 group-hover:scale-110 transition-transform duration-300">
                  {spec.value}
                </div>
                <div className="luxury-caption text-gray-400 mb-1">{spec.label}</div>
                <div className="text-xs text-gray-500">{spec.description}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Premium Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex flex-col items-center space-y-2 luxury-animate-float">
          <span className="luxury-caption text-gray-500">Discover More</span>
          <ChevronDown className="text-white/60 w-6 h-6" />
        </div>
      </div>

      {/* Ambient Light Effects */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/5 rounded-full blur-3xl luxury-animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl luxury-animate-float" style={{ animationDelay: '2s' }}></div>
    </section>
  );
};

export default PremiumHeroSection;