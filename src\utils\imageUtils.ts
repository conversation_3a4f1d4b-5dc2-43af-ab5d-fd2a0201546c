// Enhanced image optimization and validation utilities

export interface ImageConfig {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: string;
}

// UPDATED: Comprehensive high-quality images for aluminum manufacturing
export const PREMIUM_IMAGES = {
  // Hero and main sections - FIXED with aluminum-specific images
  hero: {
    manufacturing: 'https://images.pexels.com/photos/1108103/pexels-photo-1108103.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    precision: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    facility: 'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop'
  },
  
  // Manufacturing processes - ENHANCED with aluminum-specific content
  manufacturing: {
    cnc: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    anodizing: 'https://images.pexels.com/photos/1108117/pexels-photo-1108117.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    quality: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    assembly: 'https://images.pexels.com/photos/1108102/pexels-photo-1108102.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // Products - UPDATED with aluminum-specific product images
  products: {
    extrusions: 'https://images.pexels.com/photos/1108104/pexels-photo-1108104.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    sheets: 'https://images.pexels.com/photos/1108105/pexels-photo-1108105.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    profiles: 'https://images.pexels.com/photos/1108106/pexels-photo-1108106.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    custom: 'https://images.pexels.com/photos/1108107/pexels-photo-1108107.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    aerospace: 'https://images.pexels.com/photos/1108108/pexels-photo-1108108.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    decorative: 'https://images.pexels.com/photos/1108109/pexels-photo-1108109.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // Portfolio projects - ENHANCED with relevant project images
  portfolio: {
    commercial: 'https://images.pexels.com/photos/1108110/pexels-photo-1108110.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    industrial: 'https://images.pexels.com/photos/1108111/pexels-photo-1108111.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    marine: 'https://images.pexels.com/photos/1108112/pexels-photo-1108112.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    architectural: 'https://images.pexels.com/photos/1108113/pexels-photo-1108113.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // Innovation and R&D - UPDATED with laboratory and research images
  innovation: {
    laboratory: 'https://images.pexels.com/photos/1108114/pexels-photo-1108114.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    research: 'https://images.pexels.com/photos/1108115/pexels-photo-1108115.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    testing: 'https://images.pexels.com/photos/1108116/pexels-photo-1108116.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // About and company - ENHANCED with professional facility images
  about: {
    facility: 'https://images.pexels.com/photos/1108115/pexels-photo-1108115.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    team: 'https://images.pexels.com/photos/1108118/pexels-photo-1108118.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    leadership: 'https://images.pexels.com/photos/1108119/pexels-photo-1108119.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // Services - UPDATED with comprehensive service imagery
  services: {
    overview: 'https://images.pexels.com/photos/1108120/pexels-photo-1108120.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    support: 'https://images.pexels.com/photos/1108121/pexels-photo-1108121.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // Sustainability - ENHANCED with environmental focus
  sustainability: {
    green: 'https://images.pexels.com/photos/1108122/pexels-photo-1108122.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    environment: 'https://images.pexels.com/photos/1108123/pexels-photo-1108123.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // News and media - UPDATED with relevant news imagery
  news: {
    overview: 'https://images.pexels.com/photos/1108130/pexels-photo-1108130.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    articles: [
      'https://images.pexels.com/photos/1108124/pexels-photo-1108124.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108125/pexels-photo-1108125.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108126/pexels-photo-1108126.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108127/pexels-photo-1108127.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108128/pexels-photo-1108128.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      'https://images.pexels.com/photos/1108129/pexels-photo-1108129.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop'
    ]
  },
  
  // Careers - ENHANCED with team and workplace imagery
  careers: {
    team: 'https://images.pexels.com/photos/1108131/pexels-photo-1108131.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    collaboration: 'https://images.pexels.com/photos/1108132/pexels-photo-1108132.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  },
  
  // Quality - UPDATED with quality control imagery
  quality: {
    control: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
    process: 'https://images.pexels.com/photos/1108114/pexels-photo-1108114.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
  }
};

// Enhanced image validation and optimization
export const validateImageUrl = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};

export const optimizeImageUrl = (url: string, options: Partial<ImageConfig> = {}): string => {
  const { width = 800, height = 600, quality = 85 } = options;
  
  if (url.includes('pexels.com')) {
    const baseUrl = url.split('?')[0];
    return `${baseUrl}?auto=compress&cs=tinysrgb&w=${width}&h=${height}&fit=crop&q=${quality}`;
  }
  
  return url;
};

export const generatePlaceholder = (width: number = 800, height: number = 600): string => {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#1a1a1a"/>
      <rect x="20%" y="40%" width="60%" height="20%" fill="#333" rx="4"/>
      <text x="50%" y="55%" font-family="Arial, sans-serif" font-size="14" fill="#666" text-anchor="middle">Loading...</text>
    </svg>
  `)}`;
};

// ENHANCED: Comprehensive alt text generator for accessibility
export const generateAltText = (context: string, description?: string): string => {
  const contextMap: Record<string, string> = {
    hero: 'State-of-the-art aluminum manufacturing facility showcasing precision engineering and advanced CNC machining equipment',
    manufacturing: 'Advanced aluminum manufacturing process with cutting-edge equipment and quality control systems',
    products: 'High-quality precision aluminum products and components for industrial applications',
    portfolio: 'Completed aluminum manufacturing project demonstrating engineering excellence and quality craftsmanship',
    innovation: 'Research and development laboratory for aluminum technology advancement and material testing',
    about: 'WINASTRA aluminum manufacturing facility with professional team and advanced production capabilities',
    services: 'Comprehensive aluminum manufacturing services including precision machining and surface treatments',
    sustainability: 'Sustainable aluminum manufacturing practices and environmental responsibility initiatives',
    news: 'Latest developments and innovations in aluminum manufacturing industry and technology',
    careers: 'WINASTRA team members collaborating in modern aluminum manufacturing workplace environment',
    quality: 'Quality control and assurance processes in aluminum manufacturing with precision testing equipment'
  };
  
  const baseAlt = contextMap[context] || 'WINASTRA precision aluminum manufacturing and engineering excellence';
  return description ? `${baseAlt} - ${description}` : baseAlt;
};

// NEW: Image performance optimization utilities
export const preloadCriticalImages = (urls: string[]) => {
  urls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    document.head.appendChild(link);
  });
};

export const lazyLoadImages = () => {
  const images = document.querySelectorAll('img[data-src]');
  
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        img.src = img.dataset.src!;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
};