import { Droplets, Recycle, Sun, Wind, Zap } from 'lucide-react';
import React from 'react';

const SustainabilityPage: React.FC = () => {
  const initiatives = [
    {
      icon: Recycle,
      title: 'Circular Economy',
      description: 'Comprehensive recycling programs and waste reduction strategies',
      metrics: '95% Material Recovery',
      details: 'We maintain a closed-loop system where aluminum waste is continuously recycled back into production.'
    },
    {
      icon: Zap,
      title: 'Energy Efficiency',
      description: 'Renewable energy sources and optimized manufacturing processes',
      metrics: '40% Energy Reduction',
      details: 'Solar panels and energy-efficient equipment have significantly reduced our carbon footprint.'
    },
    {
      icon: Droplets,
      title: 'Water Conservation',
      description: 'Advanced water treatment and recycling systems',
      metrics: '60% Water Savings',
      details: 'Closed-loop water systems and advanced filtration reduce water consumption and waste.'
    },
    {
      icon: Wind,
      title: 'Emission Control',
      description: 'Advanced filtration and emission reduction technologies',
      metrics: '50% Emission Reduction',
      details: 'State-of-the-art air filtration systems ensure minimal environmental impact.'
    }
  ];

  const goals = [
    { year: '2025', target: 'Carbon Neutral Operations', progress: 75 },
    { year: '2027', target: '100% Renewable Energy', progress: 60 },
    { year: '2030', target: 'Zero Waste to Landfill', progress: 85 },
    { year: '2035', target: 'Net Positive Impact', progress: 25 }
  ];

  const certifications = [
    'ISO 14001 Environmental Management',
    'LEED Green Building Certification',
    'Energy Star Partnership',
    'Responsible Aluminum Certification'
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link sr-only focus:not-sr-only">
        Skip to main content
      </a>

      {/* Hero Section */}
      <section id="main-content" className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.pexels.com/photos/1108122/pexels-photo-1108122.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="Sustainable manufacturing"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">SUSTAINABLE</span>
            <br />
            <span className="text-white font-light">FUTURE</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Leading the industry in environmental responsibility through innovative
            sustainable manufacturing practices and circular economy principles.
          </p>
        </div>
      </section>

      {/* Sustainability Initiatives */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              Our Initiatives
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Comprehensive sustainability programs that minimize environmental impact
              while maximizing operational efficiency.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {initiatives.map((initiative, index) => (
              <div
                key={index}
                className="group hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="glass-effect rounded-2xl p-8 h-full">
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                      <initiative.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {initiative.title}
                      </h3>
                      <div className="text-2xl font-light text-green-400 mono-font mb-2">
                        {initiative.metrics}
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-400 mb-4 leading-relaxed">
                    {initiative.description}
                  </p>

                  <p className="text-gray-300 text-sm leading-relaxed">
                    {initiative.details}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Sustainability Goals */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              2030 Roadmap
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Our ambitious sustainability goals with measurable targets and timelines
              for achieving environmental excellence.
            </p>
          </div>

          <div className="space-y-8">
            {goals.map((goal, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-2xl font-light metallic-gradient mono-font">
                      {goal.year}
                    </div>
                    <h3 className="text-lg font-semibold text-white">
                      {goal.target}
                    </h3>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-light text-green-400 mono-font">
                      {goal.progress}%
                    </div>
                    <div className="text-sm text-gray-400">Progress</div>
                  </div>
                </div>

                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${goal.progress}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Environmental Impact */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">
                Environmental Impact
              </h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Our commitment to environmental stewardship goes beyond compliance.
                We actively work to create positive environmental impact through
                innovative technologies and responsible practices.
              </p>

              <div className="grid grid-cols-2 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-3xl font-light text-green-400 mono-font mb-2">-50%</div>
                  <div className="text-sm text-gray-400">Carbon Emissions</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-light text-blue-400 mono-font mb-2">-60%</div>
                  <div className="text-sm text-gray-400">Water Usage</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-light text-yellow-400 mono-font mb-2">95%</div>
                  <div className="text-sm text-gray-400">Waste Recycled</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-light text-purple-400 mono-font mb-2">100%</div>
                  <div className="text-sm text-gray-400">Renewable Energy</div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white mb-4">Environmental Certifications</h4>
                {certifications.map((cert, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-full"></div>
                    <span className="text-gray-300">{cert}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="animate-on-scroll">
              <img
                src="https://images.pexels.com/photos/1108123/pexels-photo-1108123.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Environmental initiatives"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Green Technology */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-light text-white mb-6">
                Green Technology Integration
              </h3>
              <p className="text-gray-400 max-w-3xl mx-auto leading-relaxed">
                Cutting-edge technologies that enable sustainable manufacturing
                while maintaining the highest quality standards.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                { icon: Sun, title: 'Solar Power', description: 'Renewable energy generation' },
                { icon: Wind, title: 'Smart Ventilation', description: 'Energy-efficient air systems' },
                { icon: Droplets, title: 'Water Recovery', description: 'Advanced filtration systems' }
              ].map((tech, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center mx-auto mb-4">
                    <tech.icon className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">{tech.title}</h4>
                  <p className="text-gray-400 text-sm">{tech.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SustainabilityPage;