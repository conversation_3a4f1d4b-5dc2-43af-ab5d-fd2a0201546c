import { ChevronDown, <PERSON>, Sparkles, Award, Shield, Zap } from 'lucide-react';
import React, { useEffect, useState } from 'react';

const HeroSection: React.FC = () => {
  const [scrollY, setScrollY] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setScrollY(window.scrollY);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleExploreClick = () => {
    const manufacturingSection = document.getElementById('manufacturing');
    if (manufacturingSection) {
      manufacturingSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <div className="relative w-full h-full">
          <img
            src="https://images.pexels.com/photos/1108103/pexels-photo-1108103.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="State-of-the-art aluminum manufacturing facility with precision CNC machining equipment"
            className="w-full h-full object-cover"
            style={{ 
              transform: `translateY(${scrollY * 0.3}px) scale(${1 + scrollY * 0.0002})`,
              filter: 'brightness(0.6) contrast(1.2) saturate(1.1)'
            }}
            loading="eager"
          />
          
          {/* Gradient overlays */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/90 via-black/60 to-black/95"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-transparent to-black/80"></div>
          
          {/* Pattern Overlay */}
          <div 
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.4) 0%, transparent 50%),
                               radial-gradient(circle at 75% 75%, rgba(192, 192, 192, 0.3) 0%, transparent 50%)`,
              backgroundSize: '400px 400px',
              animation: 'float 8s ease-in-out infinite'
            }}
          ></div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center max-w-7xl mx-auto px-4 pt-20">
        <div className={`transition-all duration-1200 ${isLoaded ? 'animate-fade-in' : 'opacity-0'}`}>
          {/* Brand Mark */}
          <div className="flex items-center justify-center mb-8">
            <Sparkles className="w-6 h-6 text-yellow-400 mr-3 animate-float" />
            <span className="text-sm text-gray-100 font-semibold tracking-wider uppercase">
              PRECISION ALUMINUM MANUFACTURING
            </span>
            <Sparkles className="w-6 h-6 text-yellow-400 ml-3 animate-float" style={{ animationDelay: '1s' }} />
          </div>

          {/* Main Heading */}
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-thin mb-8 tracking-tight leading-none">
            <span 
              className="metallic-gradient block drop-shadow-2xl"
              style={{ 
                animationDelay: '0.3s',
                textShadow: '0 4px 20px rgba(0,0,0,0.8)'
              }}
            >
              PRECISION
            </span>
            <span 
              className="text-white block font-light mt-2 drop-shadow-2xl"
              style={{ 
                animationDelay: '0.6s',
                textShadow: '0 4px 20px rgba(0,0,0,0.8)'
              }}
            >
              CRAFTED
            </span>
          </h1>

          {/* Subtitle */}
          <div className="mb-8" style={{ animationDelay: '0.9s' }}>
            <p className="text-xl md:text-2xl text-white mb-4 max-w-4xl mx-auto font-medium drop-shadow-lg">
              Where Engineering Excellence Meets Artisanal Mastery
            </p>
            <div className="w-32 h-0.5 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto"></div>
          </div>

          <p 
            className="text-lg text-gray-200 mb-12 max-w-3xl mx-auto leading-relaxed drop-shadow-lg"
            style={{ animationDelay: '1.2s' }}
          >
            Transforming premium aluminum into extraordinary solutions through cutting-edge technology, 
            uncompromising quality, and decades of specialized expertise in precision manufacturing.
          </p>

          {/* CTA Buttons */}
          <div 
            className="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8 mb-16"
            style={{ animationDelay: '1.5s' }}
          >
            <button
              onClick={handleExploreClick}
              className="metallic-button group relative overflow-hidden px-10 py-5 text-lg font-semibold rounded-xl hover:scale-105 transition-transform duration-300"
              aria-label="Explore our manufacturing capabilities"
            >
              <span className="relative z-10 flex items-center space-x-3">
                <span>Explore Excellence</span>
                <ChevronDown size={20} className="group-hover:translate-y-1 transition-transform duration-300" />
              </span>
            </button>

            <button
              className="glass-effect group flex items-center space-x-4 px-8 py-5 rounded-xl hover:bg-white/10 transition-colors duration-300"
              aria-label="Watch our manufacturing process"
            >
              <div className="w-14 h-14 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Play size={18} className="ml-1 text-yellow-400" />
              </div>
              <span className="text-lg font-semibold text-white">Watch Process</span>
            </button>
          </div>

          {/* Technical Specifications */}
          <div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"
            style={{ animationDelay: '1.8s' }}
          >
            {[
              { 
                icon: Award,
                value: '99.9%', 
                label: 'Purity', 
                description: 'Material Excellence',
                detail: 'Aerospace-grade aluminum alloys with certified composition'
              },
              { 
                icon: Shield,
                value: '±0.01mm', 
                label: 'Tolerance', 
                description: 'Precision Engineering',
                detail: 'CNC machining accuracy exceeding industry standards'
              },
              { 
                icon: Zap,
                value: 'ISO 9001', 
                label: 'Certified', 
                description: 'Quality Assurance',
                detail: 'International quality management certification'
              }
            ].map((spec, index) => (
              <div 
                key={index}
                className="glass-effect rounded-2xl p-8 hover-lift group relative backdrop-blur-md"
                style={{ animationDelay: `${2.1 + index * 0.2}s` }}
              >
                <div className="flex items-center justify-center mb-4">
                  <spec.icon className="w-8 h-8 text-yellow-400 group-hover:scale-110 transition-transform duration-300" />
                </div>
                <div className="metallic-gradient text-4xl font-light mono-font mb-3 group-hover:scale-105 transition-transform duration-300">
                  {spec.value}
                </div>
                <div className="text-sm text-gray-200 mb-2 font-semibold">{spec.label}</div>
                <div className="text-sm text-gray-300 mb-2 font-medium">{spec.description}</div>
                <div className="text-xs text-gray-400 leading-relaxed">{spec.detail}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex flex-col items-center space-y-3 animate-float">
          <span className="text-sm text-gray-300 font-medium">Discover More</span>
          <div className="w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-300 rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </div>

      {/* Ambient Light Effects */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
    </section>
  );
};

export default HeroSection;