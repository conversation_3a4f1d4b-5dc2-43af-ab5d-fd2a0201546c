import React, { useEffect, useState } from 'react';

interface MicroInteractionProps {
  children: React.ReactNode;
  type?: 'hover' | 'click' | 'scroll';
  delay?: number;
  className?: string;
}

const PremiumMicroInteractions: React.FC<MicroInteractionProps> = ({
  children,
  type = 'hover',
  delay = 0,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isInteracted, setIsInteracted] = useState(false);

  useEffect(() => {
    if (type === 'scroll') {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setTimeout(() => setIsVisible(true), delay);
          }
        },
        { threshold: 0.1 }
      );

      const element = document.querySelector(`.${className}`);
      if (element) observer.observe(element);

      return () => observer.disconnect();
    }
  }, [type, delay, className]);

  const handleInteraction = () => {
    if (type === 'click') {
      setIsInteracted(!isInteracted);
    }
  };

  const getInteractionClasses = () => {
    switch (type) {
      case 'hover':
        return 'luxury-hover-lift luxury-hover-glow';
      case 'click':
        return isInteracted ? 'luxury-animate-scale' : '';
      case 'scroll':
        return isVisible ? 'luxury-animate-fade-up' : 'opacity-0';
      default:
        return '';
    }
  };

  return (
    <div
      className={`${getInteractionClasses()} ${className}`}
      onClick={handleInteraction}
      onMouseEnter={() => type === 'hover' && setIsInteracted(true)}
      onMouseLeave={() => type === 'hover' && setIsInteracted(false)}
    >
      {children}
    </div>
  );
};

export default PremiumMicroInteractions;