/* WINASTRA Design System */

:root {
  /* Color System */
  --color-primary-black: #0a0a0a;
  --color-secondary-black: #1a1a1a;
  --color-tertiary-black: #2a2a2a;

  --color-metallic-silver: #c0c0c0;
  --color-metallic-gold: #d4af37;
  --color-metallic-bronze: #cd7f32;

  --color-accent-blue: #0066cc;
  --color-accent-green: #00cc66;
  --color-accent-red: #cc0066;

  --color-text-primary: #ffffff;
  --color-text-secondary: #e5e5e5;
  --color-text-muted: #a0a0a0;
  --color-text-disabled: #666666;

  --color-surface-primary: rgba(255, 255, 255, 0.05);
  --color-surface-secondary: rgba(255, 255, 255, 0.1);
  --color-surface-tertiary: rgba(255, 255, 255, 0.15);

  /* Typography Scale */
  --font-family-primary: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", monospace;

  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */
  --font-size-6xl: 3.75rem; /* 60px */
  --font-size-7xl: 4.5rem; /* 72px */
  --font-size-8xl: 6rem; /* 96px */

  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-black: 900;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing Scale (8px base) */
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem; /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem; /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem; /* 24px */
  --space-8: 2rem; /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem; /* 48px */
  --space-16: 4rem; /* 64px */
  --space-20: 5rem; /* 80px */
  --space-24: 6rem; /* 96px */
  --space-32: 8rem; /* 128px */

  /* Border Radius */
  --radius-sm: 0.25rem; /* 4px */
  --radius-base: 0.5rem; /* 8px */
  --radius-lg: 0.75rem; /* 12px */
  --radius-xl: 1rem; /* 16px */
  --radius-2xl: 1.5rem; /* 24px */
  --radius-3xl: 2rem; /* 32px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Component Base Classes */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver),
    #e8e8e8,
    var(--color-metallic-silver)
  );
  color: var(--color-primary-black);
}

.btn-secondary {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-surface-secondary);
}

.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid transparent;
}

.card {
  background: var(--color-surface-primary);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-surface-secondary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
}

.glass-effect {
  background: var(--color-surface-primary);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-surface-secondary);
}

.metallic-gradient {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver) 0%,
    #e8e8e8 25%,
    var(--color-metallic-silver) 50%,
    #a8a8a8 75%,
    var(--color-metallic-silver) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metallic-button {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver) 0%,
    #e8e8e8 25%,
    var(--color-metallic-silver) 50%,
    #a8a8a8 75%,
    var(--color-metallic-silver) 100%
  );
  color: var(--color-primary-black);
  border: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
}

.metallic-button:hover {
  background: linear-gradient(
    135deg,
    #d4af37 0%,
    #f4d03f 25%,
    #d4af37 50%,
    #b7950b 75%,
    #d4af37 100%
  );
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(
    135deg,
    var(--color-metallic-silver),
    var(--color-metallic-gold)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform var(--transition-base),
    box-shadow var(--transition-base);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Typography */
@media (max-width: 768px) {
  :root {
    --font-size-5xl: 2.5rem; /* 40px */
    --font-size-6xl: 3rem; /* 48px */
    --font-size-7xl: 3.5rem; /* 56px */
    --font-size-8xl: 4rem; /* 64px */
  }
}
