import React, { useEffect, useState } from 'react';

const PremiumScrollProgress: React.FC = () => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateScrollProgress = () => {
      const scrollPx = document.documentElement.scrollTop;
      const winHeightPx = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scrolled = scrollPx / winHeightPx;
      setScrollProgress(scrolled);
    };

    window.addEventListener('scroll', updateScrollProgress, { passive: true });
    return () => window.removeEventListener('scroll', updateScrollProgress);
  }, []);

  return (
    <div className="luxury-scroll-indicator">
      <div
        className="h-full bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 transition-transform duration-150 ease-out"
        style={{ transform: `scaleX(${scrollProgress})` }}
      />
    </div>
  );
};

export default PremiumScrollProgress;