import React, { createContext, useContext, useState } from 'react';
import App from '../App';
import AboutPage from '../pages/AboutPage';
import CareersPage from '../pages/CareersPage';
import NewsPage from '../pages/NewsPage';
import QualityPage from '../pages/QualityPage';
import ServicesPage from '../pages/ServicesPage';
import SustainabilityPage from '../pages/SustainabilityPage';

interface RouterContextType {
  currentPage: string;
  setCurrentPage: (page: string) => void;
}

const RouterContext = createContext<RouterContextType | undefined>(undefined);

export const useRouter = () => {
  const context = useContext(RouterContext);
  if (!context) {
    throw new Error('useRouter must be used within a RouterProvider');
  }
  return context;
};

const Router: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('home');

  const renderPage = () => {
    switch (currentPage) {
      case 'about':
        return <AboutPage />;
      case 'services':
        return <ServicesPage />;
      case 'quality':
        return <QualityPage />;
      case 'sustainability':
        return <SustainabilityPage />;
      case 'news':
        return <NewsPage />;
      case 'careers':
        return <CareersPage />;
      default:
        return <App />;
    }
  };

  return (
    <RouterContext.Provider value={{ currentPage, setCurrentPage }}>
      <div>
        {renderPage()}
      </div>
    </RouterContext.Provider>
  );
};

export default Router;