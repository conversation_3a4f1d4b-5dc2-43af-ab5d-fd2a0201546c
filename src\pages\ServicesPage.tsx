import { Award, Cog, HeadphonesIcon, Microscope, Shield, Truck, Wrench, Zap } from 'lucide-react';
import React from 'react';

const ServicesPage: React.FC = () => {
  const services = [
    {
      icon: Cog,
      title: 'Custom Fabrication',
      description: 'Precision manufacturing of bespoke aluminum components to exact specifications',
      features: ['CNC Machining', 'Complex Geometries', 'Tight Tolerances', 'Rapid Prototyping'],
      image: 'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      icon: Zap,
      title: 'Surface Treatment',
      description: 'Advanced finishing solutions for enhanced durability and aesthetics',
      features: ['Anodizing', 'Powder Coating', 'Chemical Etching', 'Polishing'],
      image: 'https://images.pexels.com/photos/1108117/pexels-photo-1108117.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      icon: Shield,
      title: 'Quality Assurance',
      description: 'Comprehensive testing and certification to ensure product excellence',
      features: ['Material Testing', 'Dimensional Inspection', 'Performance Validation', 'Certification'],
      image: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      icon: Award,
      title: 'Engineering Support',
      description: 'Expert consultation and design optimization for your projects',
      features: ['Design Review', 'Material Selection', 'Process Optimization', 'Technical Documentation'],
      image: 'https://images.pexels.com/photos/1108102/pexels-photo-1108102.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      icon: Wrench,
      title: 'Assembly Services',
      description: 'Complete assembly and integration of complex aluminum systems',
      features: ['Sub-assembly', 'Final Assembly', 'Testing & Validation', 'Packaging'],
      image: 'https://images.pexels.com/photos/1108103/pexels-photo-1108103.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      icon: Microscope,
      title: 'R&D Services',
      description: 'Innovation and development of next-generation aluminum solutions',
      features: ['Material Research', 'Process Development', 'Prototype Testing', 'Technology Transfer'],
      image: 'https://images.pexels.com/photos/1108114/pexels-photo-1108114.jpeg?auto=compress&cs=tinysrgb&w=600'
    }
  ];

  const capabilities = [
    { title: 'Material Grades', value: '50+', description: 'Different aluminum alloys' },
    { title: 'Tolerance', value: '±0.01mm', description: 'Precision manufacturing' },
    { title: 'Capacity', value: '5000T', description: 'Monthly production' },
    { title: 'Certifications', value: 'ISO 9001', description: 'Quality standards' }
  ];

  const industries = [
    { name: 'Aerospace', description: 'Critical components for aviation industry' },
    { name: 'Automotive', description: 'Lightweight solutions for vehicles' },
    { name: 'Marine', description: 'Corrosion-resistant marine applications' },
    { name: 'Architecture', description: 'Structural and decorative elements' },
    { name: 'Electronics', description: 'Heat sinks and enclosures' },
    { name: 'Medical', description: 'Precision medical device components' }
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link sr-only focus:not-sr-only">
        Skip to main content
      </a>

      {/* Hero Section */}
      <section id="main-content" className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.pexels.com/photos/1108120/pexels-photo-1108120.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="WINASTRA services"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">COMPREHENSIVE</span>
            <br />
            <span className="text-white font-light">SERVICES</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            From concept to completion, we provide end-to-end aluminum manufacturing
            solutions tailored to your specific requirements.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              Our Services
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Comprehensive aluminum manufacturing services designed to meet the most demanding requirements.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="group hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="glass-effect rounded-2xl overflow-hidden h-full">
                  <div className="relative overflow-hidden">
                    <img
                      src={service.image}
                      alt={service.title}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <div className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-md flex items-center justify-center">
                        <service.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-white mb-3">
                      {service.title}
                    </h3>
                    <p className="text-gray-400 mb-4 leading-relaxed">
                      {service.description}
                    </p>

                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-sm text-gray-300 flex items-center">
                          <div className="w-1.5 h-1.5 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Capabilities */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              Our Capabilities
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Advanced manufacturing capabilities that set us apart in the industry.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {capabilities.map((capability, index) => (
              <div
                key={index}
                className="text-center animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="glass-effect rounded-2xl p-6">
                  <div className="text-4xl font-light metallic-gradient mono-font mb-2">
                    {capability.value}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">
                    {capability.title}
                  </div>
                  <div className="text-sm text-gray-400">
                    {capability.description}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Process Flow */}
          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <h3 className="text-3xl font-light text-white mb-8 text-center">
              Our Process
            </h3>

            <div className="grid md:grid-cols-4 gap-8">
              {[
                { step: '01', title: 'Consultation', description: 'Understanding your requirements' },
                { step: '02', title: 'Design', description: 'Engineering optimal solutions' },
                { step: '03', title: 'Manufacturing', description: 'Precision fabrication' },
                { step: '04', title: 'Delivery', description: 'Quality assurance & shipping' }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center mx-auto mb-4">
                    <span className="text-black font-bold mono-font">{process.step}</span>
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">{process.title}</h4>
                  <p className="text-gray-400 text-sm">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industries Served */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              Industries We Serve
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Delivering specialized aluminum solutions across diverse industries worldwide.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <h3 className="text-xl font-semibold text-white mb-3">
                  {industry.name}
                </h3>
                <p className="text-gray-400">
                  {industry.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Support Services */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">
                Support Services
              </h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Beyond manufacturing, we provide comprehensive support services to ensure
                your success throughout the entire project lifecycle.
              </p>

              <div className="space-y-6">
                {[
                  { icon: Truck, title: 'Logistics & Shipping', description: 'Global delivery solutions' },
                  { icon: HeadphonesIcon, title: '24/7 Support', description: 'Round-the-clock assistance' },
                  { icon: Wrench, title: 'Installation Support', description: 'On-site technical guidance' },
                  { icon: Shield, title: 'Warranty Service', description: 'Comprehensive coverage' }
                ].map((support, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                      <support.icon className="w-6 h-6 text-black" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-1">{support.title}</h4>
                      <p className="text-gray-400 text-sm">{support.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="animate-on-scroll">
              <img
                src="https://images.pexels.com/photos/1108121/pexels-photo-1108121.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Support services"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;