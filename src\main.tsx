import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import EnhancedRouter from './components/EnhancedRouter';
import ErrorBoundary from './components/ErrorBoundary';
import './index.css';
import { generateDetailedReport } from './utils/websiteAudit';

// Performance monitoring and comprehensive audit
if ('performance' in window) {
  window.addEventListener('load', () => {
    const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    console.log('Page Load Performance:', {
      domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
      loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
      totalTime: perfData.loadEventEnd - perfData.fetchStart
    });

    // Run comprehensive website audit in development
    if (import.meta.env.DEV) {
      setTimeout(() => {
        // Generate detailed audit report
        generateDetailedReport();
      }, 2000);
    }
  });
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary>
      <EnhancedRouter />
    </ErrorBoundary>
  </StrictMode>
);